<?php

namespace App\Controllers;

use App\Models\ActivityUserModel;
use App\Models\ActivityModel;
use CodeIgniter\Controller;

class FieldTasks extends Controller
{
    protected $activityUserModel;
    protected $activityModel;

    public function __construct()
    {
        $this->activityUserModel = new ActivityUserModel();
        $this->activityModel = new ActivityModel();
    }

    /**
     * Check if user is logged in to field portal
     */
    private function checkFieldAuth()
    {
        // Check if logged in
        if (!session()->get('logged_in')) {
            return redirect()->to('admin')->with('error', 'Please login to access the field portal.');
        }
        return null; // Authentication successful
    }

    /**
     * Get current field user data
     */
    private function getCurrentUser()
    {
        return [
            'id' => session()->get('user_id'),
            'name' => session()->get('name'),
            'email' => session()->get('email'),
            'role' => session()->get('role')
        ];
    }

    /**
     * Display list of tasks assigned to current user
     * GET /field/tasks
     */
    public function index()
    {
        // Check authentication
        $authCheck = $this->checkFieldAuth();
        if ($authCheck) return $authCheck;

        $userId = session()->get('user_id');
        
        // Get tasks assigned to current user
        $assignedTasks = $this->activityUserModel->getActivitiesByUser($userId);
        
        // Calculate task statistics
        $totalTasks = count($assignedTasks);
        $pendingTasks = 0;
        $completedTasks = 0;
        $activeTasks = 0;
        
        foreach ($assignedTasks as $task) {
            switch ($task['status']) {
                case 'active':
                    $activeTasks++;
                    $pendingTasks++;
                    break;
                case 'submitted':
                case 'approved':
                    $completedTasks++;
                    break;
                default:
                    $pendingTasks++;
                    break;
            }
        }

        $data = [
            'title' => 'My Tasks',
            'user' => $this->getCurrentUser(),
            'tasks' => $assignedTasks,
            'stats' => [
                'total' => $totalTasks,
                'pending' => $pendingTasks,
                'completed' => $completedTasks,
                'active' => $activeTasks
            ]
        ];

        return view('field_tasks/field_tasks_list', $data);
    }

    /**
     * Display specific task details
     * GET /field/tasks/view/{id}
     */
    public function show($taskId = null)
    {
        // Check authentication
        $authCheck = $this->checkFieldAuth();
        if ($authCheck) return $authCheck;

        if (!$taskId) {
            return redirect()->to('field/tasks')->with('error', 'Task not found.');
        }

        $userId = session()->get('user_id');
        
        // Get specific task assigned to current user
        $task = $this->activityUserModel
            ->select('activity_users.*, activities.activity_name, activities.activity_type, activities.date_from, activities.date_to, activities.status, activities.remarks')
            ->join('activities', 'activities.id = activity_users.activity_id', 'left')
            ->where('activity_users.activity_id', $taskId)
            ->where('activity_users.user_id', $userId)
            ->where('activities.is_deleted', false)
            ->first();

        if (!$task) {
            return redirect()->to('field/tasks')->with('error', 'Task not found or not assigned to you.');
        }

        // Get users and locations assigned to this activity
        $activityUserModel = new \App\Models\ActivityUserModel();
        $activityLocationModel = new \App\Models\ActivityBusinessLocationModel();
        $usersAssigned = $activityUserModel
            ->select('activity_users.*, users.name, users.email')
            ->join('users', 'users.id = activity_users.user_id', 'left')
            ->where('activity_users.activity_id', $taskId)
            ->findAll();
        $locationsAssigned = $activityLocationModel
            ->select('activity_business_locations.*, business_locations.business_name, business_locations.gps_coordinates')
            ->join('business_locations', 'business_locations.id = activity_business_locations.business_location_id', 'left')
            ->where('activity_business_locations.activity_id', $taskId)
            ->findAll();

        $data = [
            'title' => 'Task Details',
            'user' => $this->getCurrentUser(),
            'task' => $task,
            'users_assigned' => $usersAssigned,
            'locations_assigned' => $locationsAssigned,
        ];

        return view('field_tasks/field_tasks_view', $data);
    }

    /**
     * Update task status
     * POST /field/tasks/update-status
     */
    public function updateStatus()
    {
        // Check authentication
        $authCheck = $this->checkFieldAuth();
        if ($authCheck) return $authCheck;

        if (!$this->request->is('post')) {
            return redirect()->to('field/tasks');
        }

        $taskId = $this->request->getPost('task_id');
        $status = $this->request->getPost('status');
        $remarks = $this->request->getPost('remarks');

        if (!$taskId || !$status) {
            return redirect()->back()->with('error', 'Invalid task or status.');
        }

        $userId = session()->get('user_id');
        
        // Verify task is assigned to current user
        $isAssigned = $this->activityUserModel->isUserAssignedToActivity($taskId, $userId);
        if (!$isAssigned) {
            return redirect()->to('field/tasks')->with('error', 'Task not found or not assigned to you.');
        }

        // Update activity status
        $updateData = [
            'status' => $status,
            'status_by' => $userId,
            'status_at' => date('Y-m-d H:i:s'),
            'updated_by' => $userId
        ];

        if ($remarks) {
            $updateData['status_remarks'] = $remarks;
        }

        $updated = $this->activityModel->update($taskId, $updateData);

        if ($updated) {
            return redirect()->to('field/tasks')->with('success', 'Task status updated successfully.');
        } else {
            return redirect()->back()->with('error', 'Failed to update task status.');
        }
    }
}
