<?php

namespace App\Controllers;

use App\Models\UserModel;
use App\Models\DakoiiOrgModel;

class AdminUsers extends BaseController
{
    protected $userModel;
    protected $dakoiiOrgModel;
    
    public function __construct()
    {
        $this->userModel = new UserModel();
        $this->dakoiiOrgModel = new DakoiiOrgModel();
    }
    
    /**
     * Check if admin is logged in
     */
    private function checkAuth()
    {
        // Check if logged in and has admin/supervisor privileges
        if (!session()->get('logged_in') ||
            (session()->get('is_admin') != 1 && session()->get('is_supervisor') != 1)) {
            return redirect()->to('admin')->with('error', 'Please login to access the admin portal.');
        }
        return null;
    }
    
    /**
     * Display list of users
     */
    public function index()
    {
        $authCheck = $this->checkAuth();
        if ($authCheck) return $authCheck;
        
        $users = $this->userModel->getUsersWithOrg();
        
        $data = [
            'title' => 'Users Management',
            'users' => $users
        ];
        
        return view('admin_users/admin_users_index', $data);
    }
    
    /**
     * Show create form
     */
    public function new()
    {
        $authCheck = $this->checkAuth();
        if ($authCheck) return $authCheck;

        // Get admin's organization info for display
        $adminOrgId = session()->get('org_id');
        $adminOrg = $this->dakoiiOrgModel->find($adminOrgId);

        // Get supervisors for reports_to dropdown
        $supervisors = $this->userModel->getSupervisorUsers();

        $data = [
            'title' => 'Create New User',
            'adminOrg' => $adminOrg,
            'supervisors' => $supervisors
        ];

        return view('admin_users/admin_users_create', $data);
    }
    
    /**
     * Handle create form submission
     */
    public function create()
    {
        $authCheck = $this->checkAuth();
        if ($authCheck) return $authCheck;
        
        $rules = [
            'name' => 'required|max_length[255]',
            'email' => 'required|valid_email|max_length[500]|is_unique[users.email]',
            'role' => 'required|in_list[user,guest]',
            'position' => 'permit_empty|max_length[255]',
            'phone' => 'permit_empty|max_length[200]'
        ];

        // Add file validation only if a file is uploaded
        $idPhotoFile = $this->request->getFile('id_photo');
        if ($idPhotoFile && $idPhotoFile->isValid() && !$idPhotoFile->hasMoved()) {
            $rules['id_photo'] = 'uploaded[id_photo]|max_size[id_photo,2048]|ext_in[id_photo,jpg,jpeg,png,gif]';
        }

        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }
        
        // Use admin's organization automatically
        $adminOrgId = session()->get('org_id');
        $org = $this->dakoiiOrgModel->find($adminOrgId);
        if (!$org || !$org['is_active']) {
            return redirect()->back()->withInput()->with('error', 'Admin organization is not available.');
        }

        // Handle file upload for ID photo
        $idPhotoPath = null;
        if ($idPhotoFile && $idPhotoFile->isValid() && !$idPhotoFile->hasMoved()) {
            // Ensure upload directory exists
            $uploadPath = FCPATH . 'uploads/id_photos/';
            if (!is_dir($uploadPath)) {
                mkdir($uploadPath, 0755, true);
            }

            $newName = $idPhotoFile->getRandomName();
            $idPhotoFile->move($uploadPath, $newName);
            $idPhotoPath = 'public/uploads/id_photos/' . $newName; // Add public/ prefix for web access
        }

        $userData = [
            'org_id' => $adminOrgId,
            'name' => $this->request->getPost('name'),
            'email' => $this->request->getPost('email'),
            'role' => $this->request->getPost('role'),
            'is_admin' => 0, // Admin portal cannot create admin users
            'is_supervisor' => $this->request->getPost('is_supervisor') ? 1 : 0,
            'reports_to' => $this->request->getPost('reports_to') ?: null,
            'position' => $this->request->getPost('position'),
            'phone' => $this->request->getPost('phone'),
            'id_photo' => $idPhotoPath,
            'status' => 'pending',
            'created_by' => session()->get('user_id')
        ];

        $userId = $this->userModel->insert($userData);

        if ($userId) {
            // Generate activation token and send email
            $token = $this->userModel->generateActivationToken($userId);

            if ($this->sendActivationEmail($userData['email'], $userData['name'], $token)) {
                return redirect()->to('admin/users')->with('success', 'User created successfully. Activation email sent to ' . $userData['email']);
            } else {
                return redirect()->to('admin/users')->with('warning', 'User created but failed to send activation email. Please resend manually.');
            }
        } else {
            return redirect()->back()->withInput()->with('error', 'Failed to create user.');
        }
    }
    
    /**
     * Display user details
     */
    public function show($id)
    {
        $authCheck = $this->checkAuth();
        if ($authCheck) return $authCheck;
        
        $user = $this->userModel->select('users.*, dakoii_org.org_name, dakoii_org.org_code, supervisor.name as supervisor_name, supervisor.email as supervisor_email')
                               ->join('dakoii_org', 'dakoii_org.id = users.org_id', 'left')
                               ->join('users as supervisor', 'supervisor.id = users.reports_to', 'left')
                               ->where('users.id', $id)
                               ->where('users.deleted_at', null)
                               ->first();
        
        if (!$user) {
            return redirect()->to('admin/users')->with('error', 'User not found.');
        }
        
        $data = [
            'title' => 'View User',
            'user' => $user
        ];
        
        return view('admin_users/admin_users_show', $data);
    }
    
    /**
     * Show edit form
     */
    public function edit($id)
    {
        $authCheck = $this->checkAuth();
        if ($authCheck) return $authCheck;

        $user = $this->userModel->find($id);

        if (!$user) {
            return redirect()->to('admin/users')->with('error', 'User not found.');
        }

        // Get admin's organization info for display
        $adminOrgId = session()->get('org_id');
        $adminOrg = $this->dakoiiOrgModel->find($adminOrgId);

        // Get supervisors for reports_to dropdown
        $supervisors = $this->userModel->getSupervisorUsers();

        $data = [
            'title' => 'Edit User',
            'user' => $user,
            'adminOrg' => $adminOrg,
            'supervisors' => $supervisors
        ];

        return view('admin_users/admin_users_edit', $data);
    }
    
    /**
     * Handle edit form submission
     */
    public function update($id)
    {
        $authCheck = $this->checkAuth();
        if ($authCheck) return $authCheck;
        
        $user = $this->userModel->find($id);
        
        if (!$user) {
            return redirect()->to('admin/users')->with('error', 'User not found.');
        }
        
        $rules = [
            'name' => 'required|max_length[255]',
            'email' => "required|valid_email|max_length[500]|is_unique[users.email,id,{$id}]",
            'role' => 'required|in_list[user,guest]',
            'position' => 'permit_empty|max_length[255]',
            'phone' => 'permit_empty|max_length[200]',
            'status' => 'required|in_list[pending,active,inactive]'
        ];

        // Add file validation only if a file is uploaded
        $idPhotoFile = $this->request->getFile('id_photo');
        if ($idPhotoFile && $idPhotoFile->isValid() && !$idPhotoFile->hasMoved()) {
            $rules['id_photo'] = 'uploaded[id_photo]|max_size[id_photo,2048]|ext_in[id_photo,jpg,jpeg,png,gif]';
        }

        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }
        
        // Use admin's organization automatically
        $adminOrgId = session()->get('org_id');
        $org = $this->dakoiiOrgModel->find($adminOrgId);
        if (!$org || !$org['is_active']) {
            return redirect()->back()->withInput()->with('error', 'Admin organization is not available.');
        }

        // Handle file upload for ID photo
        $idPhotoPath = $user['id_photo']; // Keep existing photo by default
        if ($idPhotoFile && $idPhotoFile->isValid() && !$idPhotoFile->hasMoved()) {
            // Ensure upload directory exists
            $uploadPath = FCPATH . 'uploads/id_photos/';
            if (!is_dir($uploadPath)) {
                mkdir($uploadPath, 0755, true);
            }

            // Delete old photo if it exists (remove public/ prefix for file system access)
            if (!empty($user['id_photo']) && file_exists(FCPATH . str_replace('public/', '', $user['id_photo']))) {
                unlink(FCPATH . str_replace('public/', '', $user['id_photo']));
            }

            $newName = $idPhotoFile->getRandomName();
            $idPhotoFile->move($uploadPath, $newName);
            $idPhotoPath = 'public/uploads/id_photos/' . $newName; // Add public/ prefix for web access
        }

        $userData = [
            'org_id' => $adminOrgId,
            'name' => $this->request->getPost('name'),
            'email' => $this->request->getPost('email'),
            'role' => $this->request->getPost('role'),
            'is_admin' => 0, // Admin portal cannot grant admin privileges
            'is_supervisor' => $this->request->getPost('is_supervisor') ? 1 : 0,
            'reports_to' => $this->request->getPost('reports_to') ?: null,
            'position' => $this->request->getPost('position'),
            'phone' => $this->request->getPost('phone'),
            'id_photo' => $idPhotoPath,
            'status' => $this->request->getPost('status'),
            'updated_by' => session()->get('user_id')
        ];

        // Debug: Log the data being sent to the model
        log_message('debug', 'AdminUsers update data: ' . json_encode($userData));

        // Temporarily disable model validation since we already validated in controller
        $this->userModel->skipValidation(true);
        $result = $this->userModel->update($id, $userData);
        $this->userModel->skipValidation(false); // Re-enable validation

        if ($result) {
            return redirect()->to('admin/users')->with('success', 'User updated successfully.');
        } else {
            // Get model validation errors for debugging
            $modelErrors = $this->userModel->errors();
            $errorMessage = 'Failed to update user.';
            if (!empty($modelErrors)) {
                $errorMessage .= ' Errors: ' . implode(', ', $modelErrors);
            }
            return redirect()->back()->withInput()->with('error', $errorMessage);
        }
    }

    /**
     * Send activation email to user
     */
    private function sendActivationEmail($email, $name, $token)
    {
        $emailService = \Config\Services::email();

        $activationLink = base_url("dakoii/activate-account/{$token}");

        $subject = 'Account Activation - PCOLLX System';
        $message = $this->getActivationEmailTemplate($name, $activationLink);

        $emailService->setTo($email);
        $emailService->setSubject($subject);
        $emailService->setMessage($message);

        return $emailService->send();
    }

    /**
     * Get activation email template
     */
    private function getActivationEmailTemplate($name, $activationLink)
    {
        return "
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset='UTF-8'>
            <meta name='viewport' content='width=device-width, initial-scale=1.0'>
            <title>Account Activation</title>
            <style>
                body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
                .email-container { max-width: 600px; margin: 0 auto; padding: 20px; }
                .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }
                .content { background: #f8f9fa; padding: 30px; border-radius: 0 0 10px 10px; }
                .btn { display: inline-block; background: #007bff; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; margin: 20px 0; }
                .footer { text-align: center; margin-top: 20px; color: #666; font-size: 12px; }
            </style>
        </head>
        <body>
            <div class='email-container'>
                <div class='header'>
                    <h1>🎉 Welcome to PCOLLX!</h1>
                    <p>Account Activation Required</p>
                </div>
                <div class='content'>
                    <p>Hello <strong>{$name}</strong>,</p>
                    <p>Welcome to the PCOLLX System! Your account has been created and is ready for activation.</p>
                    <p>To complete your registration and start using the system, please click the button below to activate your account:</p>

                    <div style='text-align: center;'>
                        <a href='{$activationLink}' class='btn'>Activate My Account</a>
                    </div>

                    <p><strong>Important:</strong></p>
                    <ul>
                        <li>This activation link will expire in 24 hours</li>
                        <li>You will be prompted to set your password after activation</li>
                        <li>If you didn't request this account, please ignore this email</li>
                    </ul>

                    <p>If the button doesn't work, copy and paste this link into your browser:</p>
                    <p style='word-break: break-all; background: #e9ecef; padding: 10px; border-radius: 5px;'>{$activationLink}</p>
                </div>
                <div class='footer'>
                    <p>This is an automated message from PCOLLX System. Please do not reply to this email.</p>
                </div>
            </div>
        </body>
        </html>";
    }
}
