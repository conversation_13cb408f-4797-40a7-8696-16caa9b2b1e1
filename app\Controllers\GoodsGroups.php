<?php

namespace App\Controllers;

use App\Models\GoodsGroupModel;
use CodeIgniter\Controller;

class GoodsGroups extends Controller
{
    protected $goodsGroupModel;
    
    public function __construct()
    {
        $this->goodsGroupModel = new GoodsGroupModel();
        helper('form');
    }

    /**
     * Check if user is logged in and handle field session transfer for admin/supervisor users
     */
    private function checkAuth()
    {
        // Check if logged in to admin portal
        if (!session()->get('admin_logged_in')) {
            // If user has field session and is admin or supervisor, transfer to admin session
            if (session()->get('field_logged_in') &&
                (session()->get('field_is_admin') == 1 || session()->get('field_is_supervisor') == 1)) {

                // Transfer session data to admin portal
                $sessionData = [
                    'admin_user_id' => session()->get('field_user_id'),
                    'admin_email' => session()->get('field_email'),
                    'admin_name' => session()->get('field_name'),
                    'admin_role' => session()->get('field_role'),
                    'admin_is_admin' => session()->get('field_is_admin'),
                    'admin_is_supervisor' => session()->get('field_is_supervisor'),
                    'admin_org_id' => session()->get('field_org_id'),
                    'admin_logged_in' => true
                ];

                // Set admin session (keep field session for dual access)
                session()->set($sessionData);
                return null; // Authentication successful
            } else {
                return redirect()->to('admin')->with('error', 'Please login to access this page.');
            }
        }
        return null; // Authentication successful
    }
    
    /**
     * Display list of goods groups
     */
    public function index()
    {
        $authCheck = $this->checkAuth();
        if ($authCheck) return $authCheck;
        
        $groups = $this->goodsGroupModel->getGroupsWithBrandCount();
        
        $data = [
            'title' => 'Goods Groups Management',
            'groups' => $groups
        ];
        
        return view('goods_groups/goods_groups_index', $data);
    }
    
    /**
     * Show create form
     */
    public function new()
    {
        $authCheck = $this->checkAuth();
        if ($authCheck) return $authCheck;
        
        $data = [
            'title' => 'Add New Goods Group',
            'group' => []
        ];
        
        return view('goods_groups/goods_groups_create', $data);
    }
    
    /**
     * Handle create form submission
     */
    public function create()
    {
        $authCheck = $this->checkAuth();
        if ($authCheck) return $authCheck;
        
        $rules = [
            'group_name' => 'required|max_length[100]|is_unique[goods_groups.group_name]',
            'description' => 'permit_empty|max_length[65535]'
        ];
        
        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }
        
        $data = [
            'group_name' => $this->request->getPost('group_name'),
            'description' => $this->request->getPost('description'),
            'status' => 'active', // Default status is active
            'status_by' => session()->get('admin_user_id'),
            'status_at' => date('Y-m-d H:i:s'),
            'created_by' => session()->get('admin_user_id')
        ];

        if ($this->goodsGroupModel->save($data)) {
            return redirect()->to('admin/goods-groups')->with('success', 'Goods group created successfully.');
        } else {
            return redirect()->back()->withInput()->with('error', 'Failed to create goods group.');
        }
    }
    
    /**
     * Show single goods group
     */
    public function show($id)
    {
        $authCheck = $this->checkAuth();
        if ($authCheck) return $authCheck;
        
        $group = $this->goodsGroupModel->find($id);
        
        if (!$group) {
            return redirect()->to('admin/goods-groups')->with('error', 'Goods group not found.');
        }
        
        // Get brands count for this group
        $brandModel = new \App\Models\GoodsBrandModel();
        $brandsCount = $brandModel->where('goods_group_id', $id)
                                 ->where('is_deleted', false)
                                 ->countAllResults();
        
        // Get items count for this group
        $itemModel = new \App\Models\GoodsItemModel();
        $itemsCount = $itemModel->where('goods_group_id', $id)
                               ->where('is_deleted', false)
                               ->countAllResults();
        
        $data = [
            'title' => 'View Goods Group',
            'group' => $group,
            'brands_count' => $brandsCount,
            'items_count' => $itemsCount
        ];
        
        return view('goods_groups/goods_groups_show', $data);
    }
    
    /**
     * Show edit form
     */
    public function edit($id)
    {
        $authCheck = $this->checkAuth();
        if ($authCheck) return $authCheck;
        
        $group = $this->goodsGroupModel->find($id);
        
        if (!$group) {
            return redirect()->to('admin/goods-groups')->with('error', 'Goods group not found.');
        }
        
        $data = [
            'title' => 'Edit Goods Group',
            'group' => $group
        ];
        
        return view('goods_groups/goods_groups_edit', $data);
    }
    
    /**
     * Handle edit form submission
     */
    public function update($id)
    {
        $authCheck = $this->checkAuth();
        if ($authCheck) return $authCheck;
        
        $group = $this->goodsGroupModel->find($id);
        
        if (!$group) {
            return redirect()->to('admin/goods-groups')->with('error', 'Goods group not found.');
        }

        $rules = [
            'group_name' => "required|max_length[100]|is_unique[goods_groups.group_name,id,{$id}]",
            'description' => 'permit_empty|max_length[65535]',
            'status' => 'required|in_list[active,inactive]'
        ];

        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }

        $data = [
            'group_name' => $this->request->getPost('group_name'),
            'description' => $this->request->getPost('description'),
            'status' => $this->request->getPost('status'),
            'status_by' => session()->get('dakoii_user_id'),
            'status_at' => date('Y-m-d H:i:s'),
            'updated_by' => session()->get('admin_user_id')
        ];

        if ($this->goodsGroupModel->update($id, $data)) {
            return redirect()->to('admin/goods-groups')->with('success', 'Goods group updated successfully.');
        } else {
            return redirect()->back()->withInput()->with('error', 'Failed to update goods group.');
        }
    }
    
    /**
     * Delete goods group
     */
    public function delete($id)
    {
        $authCheck = $this->checkAuth();
        if ($authCheck) return $authCheck;
        
        $group = $this->goodsGroupModel->find($id);
        
        if (!$group) {
            return redirect()->to('admin/goods-groups')->with('error', 'Goods group not found.');
        }

        // Check if group has brands
        if ($this->goodsGroupModel->hasBrands($id)) {
            return redirect()->to('admin/goods-groups')->with('error', 'Cannot delete goods group that has brands associated with it.');
        }

        // Check if group has items
        if ($this->goodsGroupModel->hasItems($id)) {
            return redirect()->to('admin/goods-groups')->with('error', 'Cannot delete goods group that has items associated with it.');
        }

        // Soft delete
        $deleteData = [
            'is_deleted' => true,
            'deleted_by' => session()->get('admin_user_id'),
            'deleted_at' => date('Y-m-d H:i:s')
        ];

        if ($this->goodsGroupModel->update($id, $deleteData)) {
            return redirect()->to('admin/goods-groups')->with('success', 'Goods group deleted successfully.');
        } else {
            return redirect()->to('admin/goods-groups')->with('error', 'Failed to delete goods group.');
        }
    }
}
