<?php

namespace App\Controllers;

use App\Models\BusinessLocationModel;

class BusinessEntitiesReport extends BaseController
{
    protected $businessLocationModel;
    
    public function __construct()
    {
        $this->businessLocationModel = new BusinessLocationModel();
    }
    
    /**
     * Check admin authentication and handle field session transfer for admin/supervisor users
     */
    private function checkAuth()
    {
        // Check if logged in to admin portal
        if (!session()->get('admin_logged_in')) {
            // If user has field session and is admin or supervisor, transfer to admin session
            if (session()->get('field_logged_in') &&
                (session()->get('field_is_admin') == 1 || session()->get('field_is_supervisor') == 1)) {

                // Transfer session data to admin portal
                $sessionData = [
                    'admin_user_id' => session()->get('field_user_id'),
                    'admin_email' => session()->get('field_email'),
                    'admin_name' => session()->get('field_name'),
                    'admin_role' => session()->get('field_role'),
                    'admin_is_admin' => session()->get('field_is_admin'),
                    'admin_is_supervisor' => session()->get('field_is_supervisor'),
                    'admin_org_id' => session()->get('field_org_id'),
                    'admin_logged_in' => true
                ];

                // Set admin session (keep field session for dual access)
                session()->set($sessionData);
                return null; // Authentication successful
            } else {
                return redirect()->to('admin/login')->with('error', 'Please login to access this page.');
            }
        }
        return null; // Authentication successful
    }
    
    /**
     * Display business entities report
     */
    public function index()
    {
        $authCheck = $this->checkAuth();
        if ($authCheck) return $authCheck;
        
        // Get all business locations with complete details
        $reportData = $this->businessLocationModel->getReportData();
        
        $data = [
            'title' => 'Business Entities Report',
            'reportData' => $reportData
        ];
        
        return view('business_entities_report/business_entities_report_index', $data);
    }
}
