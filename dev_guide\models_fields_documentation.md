# Models Fields Documentation

This document provides a comprehensive list of all model fields with their types and validation rules for the PCOLLX system.

## Table of Contents
1. [ActivityBusinessLocationModel](#activitybusinesslocationmodel)
2. [ActivityModel](#activitymodel)
3. [ActivityPriceCollectionDataModel](#activitypricecollectiondatamodel)
4. [ActivityUserModel](#activityusermodel)
5. [BusinessEntityModel](#businessentitymodel)
6. [BusinessLocationModel](#businesslocationmodel)
7. [DakoiiOrgModel](#dakoiiorgmodel)
8. [DakoiiUserModel](#dakoiiusermodel)
9. [GeoCountryModel](#geocountrymodel)
10. [GeoProvinceModel](#geoprovincemodel)
11. [GeoDistrictModel](#geodistrictmodel)
12. [GoodsBrandModel](#goodsbrandmodel)
13. [GoodsGroupModel](#goodsgroupmodel)
14. [GoodsItemModel](#goodsitemmodel)
15. [PriceDataModel](#pricedatamodel)
16. [UserModel](#usermodel)
17. [WorkplanModel](#workplanmodel)

---

## ActivityBusinessLocationModel

**Table:** `activity_business_locations`  
**Primary Key:** `id` (auto-increment)  
**Soft Deletes:** No  
**Timestamps:** No  

### Fields:
- `id` - Integer (Primary Key, Auto Increment)
- `org_id` - Integer (Required) - Organization ID
- `activity_id` - Integer (Required) - Activity ID
- `business_location_id` - Integer (Required) - Business Location ID
- `assigned_at` - DateTime (Optional) - Assignment timestamp

### Validation Rules:
- `org_id`: required|integer
- `activity_id`: required|integer
- `business_location_id`: required|integer
- `assigned_at`: permit_empty|valid_date

---

## ActivityModel

**Table:** `activities`  
**Primary Key:** `id` (auto-increment)  
**Soft Deletes:** Yes (`deleted_at`)  
**Timestamps:** Yes (`created_at`, `updated_at`)  

### Fields:
- `id` - Integer (Primary Key, Auto Increment)
- `org_id` - Integer (Required) - Organization ID
- `workplan_id` - Integer (Required) - Workplan ID
- `activity_type` - String (Required, Max 50) - Type of activity
- `activity_name` - String (Required, Max 200) - Activity name
- `date_from` - Date (Required) - Start date
- `date_to` - Date (Required) - End date
- `remarks` - Text (Optional, Max 65535) - Additional remarks
- `status` - Enum (Required) - Values: active, submitted, approved, redo, cancelled
- `status_by` - Integer (Optional) - User who set the status
- `status_at` - DateTime (Auto) - Status change timestamp
- `status_remarks` - Text (Optional, Max 65535) - Status change remarks
- `created_by` - Integer (Optional) - User who created the record
- `updated_by` - Integer (Optional) - User who last updated the record
- `created_at` - DateTime (Auto) - Creation timestamp
- `updated_at` - DateTime (Auto) - Last update timestamp
- `deleted_at` - DateTime (Soft Delete) - Deletion timestamp

### Validation Rules:
- `org_id`: required|integer
- `workplan_id`: required|integer
- `activity_type`: required|max_length[50]
- `activity_name`: required|max_length[200]
- `date_from`: required|valid_date
- `date_to`: required|valid_date
- `remarks`: permit_empty|max_length[65535]
- `status`: required|in_list[active,submitted,approved,redo,cancelled]
- `status_by`: permit_empty|integer
- `status_remarks`: permit_empty|max_length[65535]
- `created_by`: permit_empty|integer
- `updated_by`: permit_empty|integer

---

## ActivityPriceCollectionDataModel

**Table:** `activity_price_collection_data`  
**Primary Key:** `id` (auto-increment)  
**Soft Deletes:** Yes (`deleted_at`)  
**Timestamps:** Yes (`created_at`, `updated_at`)  

### Fields:
- `id` - Integer (Primary Key, Auto Increment)
- `org_id` - Integer (Required) - Organization ID
- `business_location_id` - Integer (Required) - Business Location ID
- `activity_id` - Integer (Required) - Activity ID
- `user_id` - Integer (Required) - User ID who collected the data
- `item_id` - Integer (Required) - Goods Item ID
- `price` - Decimal (Required, > 0) - Price amount
- `remarks` - Text (Optional, Max 65535) - Additional remarks
- `status` - Enum (Required) - Values: active, submitted, approved, redo, cancelled
- `status_by` - Integer (Optional) - User who set the status
- `status_at` - DateTime (Auto) - Status change timestamp
- `status_remarks` - Text (Optional, Max 65535) - Status change remarks
- `created_by` - Integer (Optional) - User who created the record
- `updated_by` - Integer (Optional) - User who last updated the record
- `created_at` - DateTime (Auto) - Creation timestamp
- `updated_at` - DateTime (Auto) - Last update timestamp
- `deleted_at` - DateTime (Soft Delete) - Deletion timestamp

### Validation Rules:
- `org_id`: required|integer
- `business_location_id`: required|integer
- `activity_id`: required|integer
- `user_id`: required|integer
- `item_id`: required|integer
- `price`: required|decimal|greater_than[0]
- `remarks`: permit_empty|max_length[65535]
- `status`: required|in_list[active,submitted,approved,redo,cancelled]
- `status_by`: permit_empty|integer
- `status_remarks`: permit_empty|max_length[65535]
- `created_by`: permit_empty|integer
- `updated_by`: permit_empty|integer

---

## ActivityUserModel

**Table:** `activity_users`  
**Primary Key:** `id` (auto-increment)  
**Soft Deletes:** No  
**Timestamps:** No  

### Fields:
- `id` - Integer (Primary Key, Auto Increment)
- `org_id` - Integer (Required) - Organization ID
- `activity_id` - Integer (Required) - Activity ID
- `user_id` - Integer (Required) - User ID
- `assigned_at` - DateTime (Optional) - Assignment timestamp

### Validation Rules:
- `org_id`: required|integer
- `activity_id`: required|integer
- `user_id`: required|integer
- `assigned_at`: permit_empty|valid_date

---

## BusinessEntityModel

**Table:** `business_entities`  
**Primary Key:** `id` (auto-increment)  
**Soft Deletes:** Yes (`deleted_at`)  
**Timestamps:** Yes (`created_at`, `updated_at`)  

### Fields:
- `id` - Integer (Primary Key, Auto Increment)
- `business_name` - String (Required, Max 150, Unique) - Business entity name
- `remarks` - Text (Optional) - Additional remarks
- `status` - Enum (Required) - Values: active, inactive
- `status_by` - Integer (Optional) - User who set the status
- `status_at` - DateTime (Auto) - Status change timestamp
- `status_remarks` - Text (Optional) - Status change remarks
- `created_by` - Integer (Optional) - User who created the record
- `updated_by` - Integer (Optional) - User who last updated the record
- `created_at` - DateTime (Auto) - Creation timestamp
- `updated_at` - DateTime (Auto) - Last update timestamp
- `deleted_at` - DateTime (Soft Delete) - Deletion timestamp

### Validation Rules:
- `business_name`: required|max_length[150]|is_unique[business_entities.business_name,id,{id}]
- `status`: required|in_list[active,inactive]
- `status_by`: permit_empty|integer
- `created_by`: permit_empty|integer
- `updated_by`: permit_empty|integer

---

## BusinessLocationModel

**Table:** `business_locations`  
**Primary Key:** `id` (auto-increment)  
**Soft Deletes:** Yes (`deleted_at`)  
**Timestamps:** Yes (`created_at`, `updated_at`)  

### Fields:
- `id` - Integer (Primary Key, Auto Increment)
- `business_entity_id` - Integer (Required) - Business Entity ID
- `business_name` - String (Required, Max 150) - Location name
- `remarks` - Text (Optional) - Additional remarks
- `country_id` - Integer (Optional) - Country ID
- `province_id` - Integer (Optional) - Province ID
- `district_id` - Integer (Optional) - District ID
- `gps_coordinates` - String (Optional, Max 200) - GPS coordinates
- `status` - Enum (Required) - Values: active, inactive
- `status_by` - Integer (Optional) - User who set the status
- `status_at` - DateTime (Auto) - Status change timestamp
- `status_remarks` - Text (Optional) - Status change remarks
- `created_by` - Integer (Optional) - User who created the record
- `updated_by` - Integer (Optional) - User who last updated the record
- `created_at` - DateTime (Auto) - Creation timestamp
- `updated_at` - DateTime (Auto) - Last update timestamp
- `deleted_at` - DateTime (Soft Delete) - Deletion timestamp

### Validation Rules:
- `business_entity_id`: required|integer
- `business_name`: required|max_length[150]
- `country_id`: permit_empty|integer
- `province_id`: permit_empty|integer
- `district_id`: permit_empty|integer
- `gps_coordinates`: permit_empty|max_length[200]
- `status`: required|in_list[active,inactive]
- `status_by`: permit_empty|integer
- `created_by`: permit_empty|integer
- `updated_by`: permit_empty|integer

---

## DakoiiOrgModel

**Table:** `dakoii_org`  
**Primary Key:** `id`  
**Soft Deletes:** Yes (`deleted_at`)  
**Timestamps:** Yes (`created_at`, `updated_at`)  

### Fields:
- `id` - Integer (Primary Key)
- `org_code` - String (Required, Max 100, Unique) - Organization code
- `org_name` - String (Required, Max 255) - Organization name
- `description` - Text (Optional) - Organization description
- `province_id` - Integer (Optional) - Province ID
- `country_id` - Integer (Optional) - Country ID
- `logo_path` - String (Optional, Max 255) - Logo file path
- `is_locationlocked` - Boolean (0/1) - Location lock status
- `postal_address` - Text (Optional) - Postal address
- `phone_numbers` - Text (Optional) - Phone numbers
- `email_addresses` - Text (Optional) - Email addresses
- `is_active` - Boolean (0/1) - Active status
- `license_status` - String (Optional, Max 50) - License status
- `created_by` - Integer (Optional) - User who created the record
- `updated_by` - Integer (Optional) - User who last updated the record
- `created_at` - DateTime (Auto) - Creation timestamp
- `updated_at` - DateTime (Auto) - Last update timestamp
- `deleted_at` - DateTime (Soft Delete) - Deletion timestamp

### Validation Rules:
- `org_code`: required|max_length[100]|is_unique[dakoii_org.org_code,id,{id}]
- `org_name`: required|max_length[255]
- `province_id`: permit_empty|integer
- `country_id`: permit_empty|integer
- `logo_path`: permit_empty|max_length[255]
- `is_locationlocked`: in_list[0,1]
- `is_active`: in_list[0,1]
- `license_status`: permit_empty|max_length[50]

---

## DakoiiUserModel

**Table:** `dakoii_users`  
**Primary Key:** `id`  
**Soft Deletes:** Yes (`deleted_at`)  
**Timestamps:** Yes (`created_at`, `updated_at`)  

### Fields:
- `id` - Integer (Primary Key)
- `name` - String (Required, Min 2, Max 255) - User full name
- `username` - String (Required, Min 3, Max 255, Unique) - Username
- `password` - String (Required, Min 6) - Password (hashed)
- `role` - String (Required, Max 100) - User role
- `is_active` - Boolean (0/1) - Active status
- `created_by` - Integer (Optional) - User who created the record
- `updated_by` - Integer (Optional) - User who last updated the record
- `created_at` - DateTime (Auto) - Creation timestamp
- `updated_at` - DateTime (Auto) - Last update timestamp
- `deleted_at` - DateTime (Soft Delete) - Deletion timestamp

### Validation Rules:
- `name`: required|min_length[2]|max_length[255]
- `username`: required|min_length[3]|max_length[255]|is_unique[dakoii_users.username,id,{id}]
- `password`: required|min_length[6]
- `role`: required|max_length[100]
- `is_active`: in_list[0,1]

---

## GeoCountryModel

**Table:** `geo_countries`
**Primary Key:** `id`
**Soft Deletes:** No
**Timestamps:** Yes (`created_at`, `updated_at`)

### Fields:
- `id` - Integer (Primary Key)
- `name` - String (Required, Max 100) - Country name
- `country_code` - String (Required, Exactly 2 chars, Unique) - ISO country code
- `created_by` - Integer (Optional) - User who created the record
- `updated_by` - Integer (Optional) - User who last updated the record
- `created_at` - DateTime (Auto) - Creation timestamp
- `updated_at` - DateTime (Auto) - Last update timestamp

### Validation Rules:
- `name`: required|max_length[100]
- `country_code`: required|exact_length[2]|is_unique[geo_countries.country_code,id,{id}]

---

## GeoProvinceModel

**Table:** `geo_provinces`
**Primary Key:** `id`
**Soft Deletes:** No
**Timestamps:** Yes (`created_at`, `updated_at`)

### Fields:
- `id` - Integer (Primary Key)
- `province_code` - String (Required, Max 10, Unique) - Province code
- `name` - String (Required, Max 100) - Province name
- `country_id` - Integer (Required) - Country ID
- `json_id` - String (Optional) - JSON identifier
- `created_by` - Integer (Optional) - User who created the record
- `updated_by` - Integer (Optional) - User who last updated the record
- `created_at` - DateTime (Auto) - Creation timestamp
- `updated_at` - DateTime (Auto) - Last update timestamp

### Validation Rules:
- `province_code`: required|max_length[10]|is_unique[geo_provinces.province_code,id,{id}]
- `name`: required|max_length[100]
- `country_id`: required|integer

---

## GeoDistrictModel

**Table:** `geo_districts`
**Primary Key:** `id`
**Soft Deletes:** No
**Timestamps:** Yes (`created_at`, `updated_at`)

### Fields:
- `id` - Integer (Primary Key)
- `district_code` - String (Optional, Max 10) - District code
- `name` - String (Required, Max 100) - District name
- `country_id` - Integer (Required) - Country ID
- `province_id` - Integer (Required) - Province ID
- `json_id` - String (Optional, Max 50) - JSON identifier
- `created_by` - Integer (Optional) - User who created the record
- `updated_by` - Integer (Optional) - User who last updated the record
- `created_at` - DateTime (Auto) - Creation timestamp
- `updated_at` - DateTime (Auto) - Last update timestamp

### Validation Rules:
- `name`: required|max_length[100]
- `country_id`: required|integer
- `province_id`: required|integer
- `district_code`: permit_empty|max_length[10]
- `json_id`: permit_empty|max_length[50]
- `created_by`: permit_empty|integer
- `updated_by`: permit_empty|integer

---

## GoodsBrandModel

**Table:** `goods_brands`
**Primary Key:** `id`
**Soft Deletes:** Yes (`deleted_at`)
**Timestamps:** Yes (`created_at`, `updated_at`)

### Fields:
- `id` - Integer (Primary Key)
- `goods_group_id` - Integer (Required) - Goods Group ID
- `brand_name` - String (Required, Max 150) - Brand name
- `type` - Enum (Required) - Values: primary, substitute
- `status` - Enum (Required) - Values: active, inactive
- `status_by` - Integer (Optional) - User who set the status
- `status_at` - DateTime (Auto) - Status change timestamp
- `status_remarks` - Text (Optional, Max 65535) - Status change remarks
- `created_by` - Integer (Optional) - User who created the record
- `updated_by` - Integer (Optional) - User who last updated the record
- `deleted_by` - Integer (Optional) - User who deleted the record
- `created_at` - DateTime (Auto) - Creation timestamp
- `updated_at` - DateTime (Auto) - Last update timestamp
- `deleted_at` - DateTime (Soft Delete) - Deletion timestamp

### Validation Rules:
- `goods_group_id`: required|integer
- `brand_name`: required|max_length[150]
- `type`: required|in_list[primary,substitute]
- `status`: required|in_list[active,inactive]
- `status_by`: permit_empty|integer
- `status_remarks`: permit_empty|max_length[65535]
- `created_by`: permit_empty|integer
- `updated_by`: permit_empty|integer

---

## GoodsGroupModel

**Table:** `goods_groups`
**Primary Key:** `id`
**Soft Deletes:** Yes (`deleted_at`)
**Timestamps:** Yes (`created_at`, `updated_at`)

### Fields:
- `id` - Integer (Primary Key)
- `group_name` - String (Required, Max 100, Unique) - Group name
- `description` - Text (Optional, Max 65535) - Group description
- `status` - Enum (Required) - Values: active, inactive
- `status_by` - Integer (Optional) - User who set the status
- `status_at` - DateTime (Auto) - Status change timestamp
- `status_remarks` - Text (Optional, Max 65535) - Status change remarks
- `created_by` - Integer (Optional) - User who created the record
- `updated_by` - Integer (Optional) - User who last updated the record
- `deleted_by` - Integer (Optional) - User who deleted the record
- `created_at` - DateTime (Auto) - Creation timestamp
- `updated_at` - DateTime (Auto) - Last update timestamp
- `deleted_at` - DateTime (Soft Delete) - Deletion timestamp

### Validation Rules:
- `group_name`: required|max_length[100]|is_unique[goods_groups.group_name,id,{id}]
- `description`: permit_empty|max_length[65535]
- `status`: required|in_list[active,inactive]
- `status_by`: permit_empty|integer
- `status_remarks`: permit_empty|max_length[65535]
- `created_by`: permit_empty|integer
- `updated_by`: permit_empty|integer

---

## GoodsItemModel

**Table:** `goods_items`
**Primary Key:** `id`
**Soft Deletes:** Yes (`deleted_at`)
**Timestamps:** Yes (`created_at`, `updated_at`)

### Fields:
- `id` - Integer (Primary Key)
- `goods_group_id` - Integer (Required) - Goods Group ID
- `goods_brand_id` - Integer (Required) - Goods Brand ID
- `item` - String (Required, Max 200) - Item name
- `remarks` - Text (Optional, Max 65535) - Item remarks
- `status` - Enum (Required) - Values: active, inactive
- `status_by` - Integer (Optional) - User who set the status
- `status_at` - DateTime (Auto) - Status change timestamp
- `status_remarks` - Text (Optional, Max 65535) - Status change remarks
- `created_by` - Integer (Optional) - User who created the record
- `updated_by` - Integer (Optional) - User who last updated the record
- `deleted_by` - Integer (Optional) - User who deleted the record
- `created_at` - DateTime (Auto) - Creation timestamp
- `updated_at` - DateTime (Auto) - Last update timestamp
- `deleted_at` - DateTime (Soft Delete) - Deletion timestamp

### Validation Rules:
- `goods_group_id`: required|integer
- `goods_brand_id`: required|integer
- `item`: required|max_length[200]
- `remarks`: permit_empty|max_length[65535]
- `status`: required|in_list[active,inactive]
- `status_by`: permit_empty|integer
- `status_remarks`: permit_empty|max_length[65535]
- `created_by`: permit_empty|integer
- `updated_by`: permit_empty|integer

---

## PriceDataModel

**Table:** `price_data`
**Primary Key:** `id` (auto-increment)
**Soft Deletes:** Yes (`deleted_at`)
**Timestamps:** Yes (`created_at`, `updated_at`)

### Fields:
- `id` - Integer (Primary Key, Auto Increment)
- `org_id` - Integer (Required) - Organization ID
- `user_id` - Integer (Required) - User ID
- `activity_id` - Integer (Required) - Activity ID
- `business_location_id` - Integer (Required) - Business Location ID
- `item_id` - Integer (Required) - Item ID
- `price_amount` - Decimal (Required, >= 0) - Price amount
- `effective_date` - Date (Required, Y-m-d format) - Effective date
- `is_active` - Boolean (0/1) - Active status
- `created_by` - Integer (Optional) - User who created the record
- `updated_by` - Integer (Optional) - User who last updated the record
- `deleted_by` - Integer (Optional) - User who deleted the record
- `created_at` - DateTime (Auto) - Creation timestamp
- `updated_at` - DateTime (Auto) - Last update timestamp
- `deleted_at` - DateTime (Soft Delete) - Deletion timestamp

### Validation Rules:
- `org_id`: required|is_natural_no_zero
- `user_id`: required|is_natural_no_zero
- `activity_id`: required|is_natural_no_zero
- `business_location_id`: required|is_natural_no_zero
- `item_id`: required|is_natural_no_zero
- `price_amount`: required|decimal|greater_than_equal_to[0]
- `effective_date`: required|valid_date[Y-m-d]
- `is_active`: in_list[0,1]
- `created_by`: permit_empty|is_natural_no_zero
- `updated_by`: permit_empty|is_natural_no_zero
- `deleted_by`: permit_empty|is_natural_no_zero

---

## UserModel

**Table:** `users`
**Primary Key:** `id`
**Soft Deletes:** Yes (`deleted_at`)
**Timestamps:** Yes (`created_at`, `updated_at`)

### Fields:
- `id` - Integer (Primary Key)
- `org_id` - Integer (Required) - Organization ID
- `sys_no` - Integer (Auto-generated) - System number (year + increment)
- `name` - String (Required, Max 255) - User full name
- `password` - String (Optional, Min 4) - Password (hashed)
- `role` - Enum (Required) - Values: user, guest
- `is_admin` - Boolean (0/1) - Admin flag
- `is_supervisor` - Boolean (0/1) - Supervisor flag
- `reports_to` - Integer (Optional) - User ID of supervisor
- `position` - String (Optional, Max 255) - Job position
- `id_photo` - String (Optional, Max 500) - ID photo file path
- `phone` - String (Optional, Max 200) - Phone number
- `email` - String (Required, Max 500, Unique) - Email address
- `status` - String (Required, Max 20) - User status
- `activation_token` - String (Optional) - Account activation token
- `activation_sent_at` - DateTime (Optional) - Token sent timestamp
- `activated_at` - DateTime (Optional) - Account activation timestamp
- `created_by` - Integer (Optional) - User who created the record
- `updated_by` - Integer (Optional) - User who last updated the record
- `deleted_by` - Integer (Optional) - User who deleted the record
- `created_at` - DateTime (Auto) - Creation timestamp
- `updated_at` - DateTime (Auto) - Last update timestamp
- `deleted_at` - DateTime (Soft Delete) - Deletion timestamp

### Validation Rules:
- `org_id`: required|integer
- `name`: required|max_length[255]
- `password`: permit_empty|min_length[4]
- `role`: required|in_list[user,guest]
- `is_admin`: permit_empty|integer|in_list[0,1]
- `is_supervisor`: permit_empty|integer|in_list[0,1]
- `reports_to`: permit_empty|integer
- `position`: permit_empty|max_length[255]
- `id_photo`: permit_empty|max_length[500]
- `phone`: permit_empty|max_length[200]
- `email`: required|valid_email|max_length[500]|is_unique[users.email,id,{id}]
- `status`: required|max_length[20]

---

## WorkplanModel

**Table:** `workplans`
**Primary Key:** `id` (auto-increment)
**Soft Deletes:** Yes (`deleted_at`)
**Timestamps:** Yes (`created_at`, `updated_at`)

### Fields:
- `id` - Integer (Primary Key, Auto Increment)
- `org_id` - Integer (Required) - Organization ID
- `supervisor_id` - Integer (Required) - Supervisor User ID
- `date_from` - Date (Required) - Start date
- `date_to` - Date (Required) - End date
- `title` - String (Required, Max 200) - Workplan title
- `remarks` - Text (Optional, Max 65535) - Additional remarks
- `status` - Enum (Required) - Values: active, inactive, completed, cancelled
- `status_by` - Integer (Optional) - User who set the status
- `status_at` - DateTime (Auto) - Status change timestamp
- `status_remarks` - Text (Optional, Max 65535) - Status change remarks
- `created_by` - Integer (Optional) - User who created the record
- `updated_by` - Integer (Optional) - User who last updated the record
- `created_at` - DateTime (Auto) - Creation timestamp
- `updated_at` - DateTime (Auto) - Last update timestamp
- `deleted_at` - DateTime (Soft Delete) - Deletion timestamp

### Validation Rules:
- `org_id`: required|integer
- `supervisor_id`: required|integer
- `date_from`: required|valid_date
- `date_to`: required|valid_date
- `title`: required|max_length[200]
- `remarks`: permit_empty|max_length[65535]
- `status`: required|in_list[active,inactive,completed,cancelled]
- `status_by`: permit_empty|integer
- `status_remarks`: permit_empty|max_length[65535]
- `created_by`: permit_empty|integer
- `updated_by`: permit_empty|integer

---

## Summary

This documentation covers all 17 models in the PCOLLX system with their complete field definitions, data types, and validation rules. The models are organized into several functional areas:

### Geographic Models:
- GeoCountryModel, GeoProvinceModel, GeoDistrictModel

### Organization & User Management:
- DakoiiOrgModel, DakoiiUserModel, UserModel

### Business Management:
- BusinessEntityModel, BusinessLocationModel

### Goods Management:
- GoodsGroupModel, GoodsBrandModel, GoodsItemModel

### Activity & Workplan Management:
- WorkplanModel, ActivityModel, ActivityUserModel, ActivityBusinessLocationModel

### Price Collection:
- ActivityPriceCollectionDataModel, PriceDataModel

### Common Patterns:
- Most models use soft deletes (`deleted_at`)
- Most models have timestamps (`created_at`, `updated_at`)
- Most models track who created/updated records (`created_by`, `updated_by`)
- Status fields typically use enum values
- Foreign key relationships are maintained through integer ID fields

**Generated on:** $(date)
**Total Models:** 17
**Total Tables:** 17
