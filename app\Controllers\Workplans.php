<?php

namespace App\Controllers;

use App\Models\WorkplanModel;
use App\Models\UserModel;
use App\Models\DakoiiOrgModel;
use App\Models\ActivityModel;
use CodeIgniter\Controller;

class Workplans extends Controller
{
    protected $workplanModel;
    protected $userModel;
    protected $dakoiiOrgModel;
    protected $activityModel;

    public function __construct()
    {
        $this->workplanModel = new WorkplanModel();
        $this->userModel = new UserModel();
        $this->dakoiiOrgModel = new DakoiiOrgModel();
        $this->activityModel = new ActivityModel();
        helper('form');
    }

    /**
     * Check if user is logged in
     */
    private function checkAuth()
    {
        // Check if logged in and has admin/supervisor privileges
        if (!session()->get('logged_in') ||
            (session()->get('is_admin') != 1 && session()->get('is_supervisor') != 1)) {
            return redirect()->to('admin')->with('error', 'Please login to access this page.');
        }
        return null;
    }
    
    /**
     * Display list of workplans
     */
    public function index()
    {
        $authCheck = $this->checkAuth();
        if ($authCheck) return $authCheck;
        
        $orgId = session()->get('org_id');
        $workplans = $this->workplanModel->select('workplans.*, users.name as supervisor_name, dakoii_org.org_name')
                                        ->join('users', 'users.id = workplans.supervisor_id', 'left')
                                        ->join('dakoii_org', 'dakoii_org.id = workplans.org_id', 'left')
                                        ->where('workplans.org_id', $orgId)
                                        ->where('workplans.is_deleted', false)
                                        ->orderBy('workplans.date_from', 'DESC')
                                        ->findAll();
        
        $data = [
            'title' => 'Workplans Management',
            'workplans' => $workplans
        ];
        
        return view('workplans/workplans_index', $data);
    }
    
    /**
     * Show create form
     */
    public function new()
    {
        $authCheck = $this->checkAuth();
        if ($authCheck) return $authCheck;

        $data = [
            'title' => 'Create New Workplan'
        ];

        return view('workplans/workplans_create', $data);
    }
    
    /**
     * Handle create form submission
     */
    public function create()
    {
        $authCheck = $this->checkAuth();
        if ($authCheck) return $authCheck;
        
        $rules = [
            'date_from' => 'required|valid_date',
            'date_to' => 'required|valid_date',
            'title' => 'required|max_length[200]',
            'remarks' => 'permit_empty'
        ];
        
        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }
        
        // Validate date range
        $dateFrom = $this->request->getPost('date_from');
        $dateTo = $this->request->getPost('date_to');
        
        if (strtotime($dateFrom) >= strtotime($dateTo)) {
            return redirect()->back()->withInput()->with('error', 'End date must be after start date.');
        }
        
        $workplanData = [
            'org_id' => session()->get('org_id'),
            'supervisor_id' => session()->get('user_id'), // Automatically set to logged-in user
            'date_from' => $dateFrom,
            'date_to' => $dateTo,
            'title' => $this->request->getPost('title'),
            'remarks' => $this->request->getPost('remarks'),
            'status' => 'active', // Automatically set to active
            'status_by' => session()->get('user_id'),
            'status_at' => date('Y-m-d H:i:s'),
            'created_by' => session()->get('user_id')
        ];
        
        if ($this->workplanModel->insert($workplanData)) {
            return redirect()->to('admin/workplans')->with('success', 'Workplan created successfully.');
        } else {
            return redirect()->back()->withInput()->with('error', 'Failed to create workplan.');
        }
    }
    
    /**
     * Show single workplan
     */
    public function show($id)
    {
        $authCheck = $this->checkAuth();
        if ($authCheck) return $authCheck;
        
        $orgId = session()->get('org_id');
        $workplan = $this->workplanModel->select('workplans.*, users.name as supervisor_name, dakoii_org.org_name')
                                       ->join('users', 'users.id = workplans.supervisor_id', 'left')
                                       ->join('dakoii_org', 'dakoii_org.id = workplans.org_id', 'left')
                                       ->where('workplans.id', $id)
                                       ->where('workplans.org_id', $orgId)
                                       ->where('workplans.is_deleted', false)
                                       ->first();
        
        if (!$workplan) {
            return redirect()->to('admin/workplans')->with('error', 'Workplan not found.');
        }

        // Get activities statistics for this workplan
        $activitiesCount = $this->activityModel->where('workplan_id', $id)
                                              ->where('org_id', $orgId)
                                              ->where('is_deleted', false)
                                              ->countAllResults();

        $activeActivitiesCount = $this->activityModel->where('workplan_id', $id)
                                                    ->where('org_id', $orgId)
                                                    ->where('status', 'active')
                                                    ->where('is_deleted', false)
                                                    ->countAllResults();

        $submittedActivitiesCount = $this->activityModel->where('workplan_id', $id)
                                                       ->where('org_id', $orgId)
                                                       ->where('status', 'submitted')
                                                       ->where('is_deleted', false)
                                                       ->countAllResults();

        $approvedActivitiesCount = $this->activityModel->where('workplan_id', $id)
                                                      ->where('org_id', $orgId)
                                                      ->where('status', 'approved')
                                                      ->where('is_deleted', false)
                                                      ->countAllResults();

        // Get recent activities for this workplan
        $recentActivities = $this->activityModel->where('workplan_id', $id)
                                               ->where('org_id', $orgId)
                                               ->where('is_deleted', false)
                                               ->orderBy('created_at', 'DESC')
                                               ->limit(5)
                                               ->find();

        $data = [
            'title' => 'Workplan Details',
            'workplan' => $workplan,
            'activities_stats' => [
                'total' => $activitiesCount,
                'active' => $activeActivitiesCount,
                'submitted' => $submittedActivitiesCount,
                'approved' => $approvedActivitiesCount
            ],
            'recent_activities' => $recentActivities
        ];

        return view('workplans/workplans_show', $data);
    }
    
    /**
     * Show edit form
     */
    public function edit($id)
    {
        $authCheck = $this->checkAuth();
        if ($authCheck) return $authCheck;
        
        $orgId = session()->get('org_id');
        $workplan = $this->workplanModel->where('id', $id)
                                       ->where('org_id', $orgId)
                                       ->where('is_deleted', false)
                                       ->first();
        
        if (!$workplan) {
            return redirect()->to('admin/workplans')->with('error', 'Workplan not found.');
        }
        
        // Get supervisors from the same organization
        $supervisors = $this->userModel->where('org_id', $orgId)
                                      ->where('is_supervisor', 1)
                                      ->where('status', 'active')
                                      ->where('deleted_at', null)
                                      ->findAll();
        
        $data = [
            'title' => 'Edit Workplan',
            'workplan' => $workplan,
            'supervisors' => $supervisors
        ];
        
        return view('workplans/workplans_edit', $data);
    }
    
    /**
     * Handle update form submission
     */
    public function update($id)
    {
        $authCheck = $this->checkAuth();
        if ($authCheck) return $authCheck;
        
        $orgId = session()->get('org_id');
        $workplan = $this->workplanModel->where('id', $id)
                                       ->where('org_id', $orgId)
                                       ->where('is_deleted', false)
                                       ->first();
        
        if (!$workplan) {
            return redirect()->to('admin/workplans')->with('error', 'Workplan not found.');
        }
        
        $rules = [
            'supervisor_id' => 'required|integer',
            'date_from' => 'required|valid_date',
            'date_to' => 'required|valid_date',
            'title' => 'required|max_length[200]',
            'status' => 'required|in_list[active,inactive,completed,cancelled]',
            'remarks' => 'permit_empty',
            'status_remarks' => 'permit_empty'
        ];
        
        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }
        
        // Validate date range
        $dateFrom = $this->request->getPost('date_from');
        $dateTo = $this->request->getPost('date_to');
        
        if (strtotime($dateFrom) >= strtotime($dateTo)) {
            return redirect()->back()->withInput()->with('error', 'End date must be after start date.');
        }
        
        $updateData = [
            'supervisor_id' => $this->request->getPost('supervisor_id'),
            'date_from' => $dateFrom,
            'date_to' => $dateTo,
            'title' => $this->request->getPost('title'),
            'remarks' => $this->request->getPost('remarks'),
            'status' => $this->request->getPost('status'),
            'status_remarks' => $this->request->getPost('status_remarks'),
            'updated_by' => session()->get('user_id')
        ];
        
        // Update status tracking if status changed
        if ($updateData['status'] !== $workplan['status']) {
            $updateData['status_by'] = session()->get('user_id');
            $updateData['status_at'] = date('Y-m-d H:i:s');
        }
        
        if ($this->workplanModel->update($id, $updateData)) {
            return redirect()->to('admin/workplans')->with('success', 'Workplan updated successfully.');
        } else {
            return redirect()->back()->withInput()->with('error', 'Failed to update workplan.');
        }
    }
    
    /**
     * Handle delete
     */
    public function delete($id)
    {
        $authCheck = $this->checkAuth();
        if ($authCheck) return $authCheck;
        
        $orgId = session()->get('org_id');
        $workplan = $this->workplanModel->where('id', $id)
                                       ->where('org_id', $orgId)
                                       ->where('is_deleted', false)
                                       ->first();
        
        if (!$workplan) {
            return redirect()->to('admin/workplans')->with('error', 'Workplan not found.');
        }
        
        $deleteData = [
            'is_deleted' => true,
            'deleted_by' => session()->get('user_id'),
            'deleted_at' => date('Y-m-d H:i:s')
        ];
        
        if ($this->workplanModel->update($id, $deleteData)) {
            return redirect()->to('admin/workplans')->with('success', 'Workplan deleted successfully.');
        } else {
            return redirect()->back()->with('error', 'Failed to delete workplan.');
        }
    }
}
