<?php

namespace App\Controllers;

use App\Models\GoodsItemModel;
use App\Models\GoodsBrandModel;
use App\Models\GoodsGroupModel;
use App\Models\GeoDistrictModel;
use CodeIgniter\Controller;

class GoodsItems extends Controller
{
    protected $goodsItemModel;
    protected $goodsBrandModel;
    protected $goodsGroupModel;
    protected $geoDistrictModel;
    
    public function __construct()
    {
        $this->goodsItemModel = new GoodsItemModel();
        $this->goodsBrandModel = new GoodsBrandModel();
        $this->goodsGroupModel = new GoodsGroupModel();
        $this->geoDistrictModel = new GeoDistrictModel();
        helper('form');
    }

    /**
     * Check if user is logged in and handle field session transfer for admin/supervisor users
     */
    private function checkAuth()
    {
        // Check if logged in to admin portal
        if (!session()->get('logged_in')) {
            // If user has field session and is admin or supervisor, transfer to admin session
            if (session()->get('field_logged_in') &&
                (session()->get('field_is_admin') == 1 || session()->get('field_is_supervisor') == 1)) {

                // Transfer session data to admin portal
                $sessionData = [
                    'user_id' => session()->get('field_user_id'),
                    'email' => session()->get('field_email'),
                    'name' => session()->get('field_name'),
                    'role' => session()->get('field_role'),
                    'is_admin' => session()->get('field_is_admin'),
                    'is_supervisor' => session()->get('field_is_supervisor'),
                    'org_id' => session()->get('field_org_id'),
                    'logged_in' => true
                ];

                // Set admin session (keep field session for dual access)
                session()->set($sessionData);
                return null; // Authentication successful
            } else {
                return redirect()->to('admin')->with('error', 'Please login to access this page.');
            }
        }
        return null; // Authentication successful
    }
    
    /**
     * Display list of goods items
     */
    public function index()
    {
        $authCheck = $this->checkAuth();
        if ($authCheck) return $authCheck;
        
        // Check filters
        $groupId = $this->request->getGet('group_id');
        $brandId = $this->request->getGet('brand_id');
        $selectedGroup = null;
        $selectedBrand = null;
        
        if ($groupId) {
            $selectedGroup = $this->goodsGroupModel->find($groupId);
            if (!$selectedGroup) {
                return redirect()->to('admin/goods-items')->with('error', 'Invalid goods group selected.');
            }
        }
        
        if ($brandId) {
            $selectedBrand = $this->goodsBrandModel->select('goods_brands.*, goods_groups.group_name')
                                                  ->join('goods_groups', 'goods_groups.id = goods_brands.goods_group_id', 'left')
                                                  ->where('goods_brands.id', $brandId)
                                                  ->first();
            if (!$selectedBrand) {
                return redirect()->to('admin/goods-items')->with('error', 'Invalid goods brand selected.');
            }
            // If brand is selected, also set the group
            $groupId = $selectedBrand['goods_group_id'];
            $selectedGroup = $this->goodsGroupModel->find($groupId);
        }
        
        // Get items based on filters
        if ($brandId) {
            $items = $this->goodsItemModel->getItemsByBrand($brandId);
        } elseif ($groupId) {
            $items = $this->goodsItemModel->getItemsByGroup($groupId);
        } else {
            $items = $this->goodsItemModel->getItemsWithDetails();
        }

        // Add group and brand names to each item
        foreach ($items as &$item) {
            $group = $this->goodsGroupModel->find($item['goods_group_id']);
            $brand = $this->goodsBrandModel->find($item['goods_brand_id']);

            $item['group_name'] = $group ? $group['group_name'] : 'Unknown';
            $item['brand_name'] = $brand ? $brand['brand_name'] : 'Unknown';
            $item['brand_type'] = $brand ? $brand['type'] : 'unknown';
        }
        
        // Get all groups and brands for filter dropdowns
        $groups = $this->goodsGroupModel->getActiveGroupsForDropdown();
        $brands = [];
        if ($groupId) {
            $brands = $this->goodsBrandModel->getActiveBrandsByGroup($groupId);
        }
        
        $data = [
            'title' => 'Goods Items Management',
            'items' => $items,
            'groups' => $groups,
            'brands' => $brands,
            'selected_group' => $selectedGroup,
            'selected_brand' => $selectedBrand,
            'group_id' => $groupId,
            'brand_id' => $brandId
        ];
        
        return view('goods_items/goods_items_index', $data);
    }
    
    /**
     * Show create form
     */
    public function new()
    {
        $authCheck = $this->checkAuth();
        if ($authCheck) return $authCheck;
        
        // Get parameters if provided
        $groupId = $this->request->getGet('group_id');
        $brandId = $this->request->getGet('brand_id');
        $selectedGroup = null;
        $selectedBrand = null;
        
        if ($brandId) {
            $selectedBrand = $this->goodsBrandModel->select('goods_brands.*, goods_groups.group_name')
                                                  ->join('goods_groups', 'goods_groups.id = goods_brands.goods_group_id', 'left')
                                                  ->where('goods_brands.id', $brandId)
                                                  ->first();
            if ($selectedBrand) {
                $groupId = $selectedBrand['goods_group_id'];
                $selectedGroup = $this->goodsGroupModel->find($groupId);
            }
        } elseif ($groupId) {
            $selectedGroup = $this->goodsGroupModel->find($groupId);
        }
        
        // Get all active groups for dropdowns
        $groups = $this->goodsGroupModel->getActiveGroupsForDropdown();
        $brands = [];
        if ($groupId) {
            $brands = $this->goodsBrandModel->getActiveBrandsByGroup($groupId);
        }

        $data = [
            'title' => 'Add New Goods Item',
            'item' => [],
            'groups' => $groups,
            'brands' => $brands,
            'selected_group' => $selectedGroup,
            'selected_brand' => $selectedBrand,
            'group_id' => $groupId,
            'brand_id' => $brandId
        ];
        
        return view('goods_items/goods_items_create', $data);
    }
    
    /**
     * Handle create form submission
     */
    public function create()
    {
        $authCheck = $this->checkAuth();
        if ($authCheck) return $authCheck;
        
        $rules = [
            'goods_group_id' => 'required|integer',
            'goods_brand_id' => 'required|integer',
            'item' => 'required|max_length[200]'
        ];
        
        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }
        
        // Validate relationships
        $group = $this->goodsGroupModel->find($this->request->getPost('goods_group_id'));
        if (!$group || $group['status'] !== 'active') {
            return redirect()->back()->withInput()->with('error', 'Selected goods group is not available.');
        }
        
        $brand = $this->goodsBrandModel->find($this->request->getPost('goods_brand_id'));
        if (!$brand || $brand['status'] !== 'active' || $brand['goods_group_id'] != $this->request->getPost('goods_group_id')) {
            return redirect()->back()->withInput()->with('error', 'Selected goods brand is not available or does not belong to the selected group.');
        }
        
        $data = [
            'goods_group_id' => $this->request->getPost('goods_group_id'),
            'goods_brand_id' => $this->request->getPost('goods_brand_id'),
            'item' => $this->request->getPost('item'),
            'status' => 'active', // Default status is active
            'status_by' => session()->get('user_id'),
            'status_at' => date('Y-m-d H:i:s'),
            'created_by' => session()->get('user_id')
        ];

        // Add optional fields
        if ($this->request->getPost('remarks')) {
            $data['remarks'] = $this->request->getPost('remarks');
        }
        
        try {
            if ($this->goodsItemModel->save($data)) {
                $redirectUrl = 'admin/goods-items';
                if ($this->request->getPost('goods_brand_id')) {
                    $redirectUrl .= '?brand_id=' . $this->request->getPost('goods_brand_id');
                } elseif ($this->request->getPost('goods_group_id')) {
                    $redirectUrl .= '?group_id=' . $this->request->getPost('goods_group_id');
                }
                return redirect()->to($redirectUrl)->with('success', 'Goods item created successfully.');
            } else {
                return redirect()->back()->withInput()->with('error', 'Failed to create goods item.');
            }
        } catch (\Exception $e) {
            return redirect()->back()->withInput()->with('error', 'Item name already exists for this brand and group combination.');
        }
    }
    
    /**
     * Show single goods item
     */
    public function show($id)
    {
        $authCheck = $this->checkAuth();
        if ($authCheck) return $authCheck;

        $item = $this->goodsItemModel->find($id);

        if (!$item || $item['is_deleted']) {
            return redirect()->to('admin/goods-items')->with('error', 'Goods item not found.');
        }

        // Get related data using simple find operations
        $group = $this->goodsGroupModel->find($item['goods_group_id']);
        $brand = $this->goodsBrandModel->find($item['goods_brand_id']);

        // Add related data to item
        $item['group_name'] = $group ? $group['group_name'] : 'Unknown';
        $item['brand_name'] = $brand ? $brand['brand_name'] : 'Unknown';
        $item['brand_type'] = $brand ? $brand['type'] : 'unknown';

        $data = [
            'title' => 'View Goods Item',
            'item' => $item
        ];

        return view('goods_items/goods_items_show', $data);
    }
    
    /**
     * Show edit form
     */
    public function edit($id)
    {
        $authCheck = $this->checkAuth();
        if ($authCheck) return $authCheck;
        
        $item = $this->goodsItemModel->find($id);
        
        if (!$item) {
            return redirect()->to('admin/goods-items')->with('error', 'Goods item not found.');
        }
        
        // Get all active groups and brands for dropdowns
        $groups = $this->goodsGroupModel->getActiveGroupsForDropdown();
        $brands = $this->goodsBrandModel->getActiveBrandsByGroup($item['goods_group_id']);

        $data = [
            'title' => 'Edit Goods Item',
            'item' => $item,
            'groups' => $groups,
            'brands' => $brands
        ];
        
        return view('goods_items/goods_items_edit', $data);
    }
    
    /**
     * Handle edit form submission
     */
    public function update($id)
    {
        $authCheck = $this->checkAuth();
        if ($authCheck) return $authCheck;
        
        $item = $this->goodsItemModel->find($id);
        
        if (!$item) {
            return redirect()->to('admin/goods-items')->with('error', 'Goods item not found.');
        }
        
        $rules = [
            'goods_group_id' => 'required|integer',
            'goods_brand_id' => 'required|integer',
            'item' => 'required|max_length[200]',
            'status' => 'required|in_list[active,inactive]'
        ];
        
        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }
        
        // Validate relationships
        $group = $this->goodsGroupModel->find($this->request->getPost('goods_group_id'));
        if (!$group || $group['status'] !== 'active') {
            return redirect()->back()->withInput()->with('error', 'Selected goods group is not available.');
        }
        
        $brand = $this->goodsBrandModel->find($this->request->getPost('goods_brand_id'));
        if (!$brand || $brand['status'] !== 'active' || $brand['goods_group_id'] != $this->request->getPost('goods_group_id')) {
            return redirect()->back()->withInput()->with('error', 'Selected goods brand is not available or does not belong to the selected group.');
        }
        
        $data = [
            'goods_group_id' => $this->request->getPost('goods_group_id'),
            'goods_brand_id' => $this->request->getPost('goods_brand_id'),
            'item' => $this->request->getPost('item'),
            'status' => $this->request->getPost('status'),
            'status_by' => session()->get('user_id'),
            'status_at' => date('Y-m-d H:i:s'),
            'updated_by' => session()->get('user_id')
        ];

        // Add optional fields
        if ($this->request->getPost('remarks')) {
            $data['remarks'] = $this->request->getPost('remarks');
        }
        
        try {
            if ($this->goodsItemModel->update($id, $data)) {
                return redirect()->to('admin/goods-items')->with('success', 'Goods item updated successfully.');
            } else {
                return redirect()->back()->withInput()->with('error', 'Failed to update goods item.');
            }
        } catch (\Exception $e) {
            return redirect()->back()->withInput()->with('error', 'Item name already exists for this brand and group combination.');
        }
    }
    
    /**
     * Delete goods item
     */
    public function delete($id)
    {
        $authCheck = $this->checkAuth();
        if ($authCheck) return $authCheck;
        
        $item = $this->goodsItemModel->find($id);
        
        if (!$item) {
            return redirect()->to('admin/goods-items')->with('error', 'Goods item not found.');
        }
        
        // Soft delete
        $deleteData = [
            'is_deleted' => true,
            'deleted_by' => session()->get('user_id'),
            'deleted_at' => date('Y-m-d H:i:s')
        ];
        
        if ($this->goodsItemModel->update($id, $deleteData)) {
            return redirect()->to('admin/goods-items')->with('success', 'Goods item deleted successfully.');
        } else {
            return redirect()->to('admin/goods-items')->with('error', 'Failed to delete goods item.');
        }
    }
    
    /**
     * AJAX endpoint to get brands by group
     */
    public function getBrandsByGroup()
    {
        $groupId = $this->request->getPost('group_id');

        if (!$groupId) {
            return $this->response->setJSON(['error' => 'Group ID is required']);
        }

        // Get brands as array of objects for AJAX
        $brands = $this->goodsBrandModel->where('goods_group_id', $groupId)
                                       ->where('status', 'active')
                                       ->where('is_deleted', false)
                                       ->orderBy('brand_name', 'ASC')
                                       ->findAll();

        return $this->response->setJSON(['brands' => $brands]);
    }
}
