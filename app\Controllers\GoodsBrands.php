<?php

namespace App\Controllers;

use App\Models\GoodsBrandModel;
use App\Models\GoodsGroupModel;
use CodeIgniter\Controller;

class GoodsBrands extends Controller
{
    protected $goodsBrandModel;
    protected $goodsGroupModel;
    
    public function __construct()
    {
        $this->goodsBrandModel = new GoodsBrandModel();
        $this->goodsGroupModel = new GoodsGroupModel();
        helper('form');
    }

    /**
     * Check if user is logged in and handle field session transfer for admin/supervisor users
     */
    private function checkAuth()
    {
        // Check if logged in to admin portal
        if (!session()->get('logged_in')) {
            // If user has field session and is admin or supervisor, transfer to admin session
            if (session()->get('field_logged_in') &&
                (session()->get('field_is_admin') == 1 || session()->get('field_is_supervisor') == 1)) {

                // Transfer session data to admin portal
                $sessionData = [
                    'user_id' => session()->get('field_user_id'),
                    'email' => session()->get('field_email'),
                    'name' => session()->get('field_name'),
                    'role' => session()->get('field_role'),
                    'is_admin' => session()->get('field_is_admin'),
                    'is_supervisor' => session()->get('field_is_supervisor'),
                    'org_id' => session()->get('field_org_id'),
                    'logged_in' => true
                ];

                // Set admin session (keep field session for dual access)
                session()->set($sessionData);
                return null; // Authentication successful
            } else {
                return redirect()->to('admin')->with('error', 'Please login to access this page.');
            }
        }
        return null; // Authentication successful
    }
    
    /**
     * Display list of goods brands
     */
    public function index()
    {
        $authCheck = $this->checkAuth();
        if ($authCheck) return $authCheck;
        
        // Check if filtering by group
        $groupId = $this->request->getGet('group_id');
        $selectedGroup = null;
        
        if ($groupId) {
            $selectedGroup = $this->goodsGroupModel->find($groupId);
            if (!$selectedGroup) {
                return redirect()->to('admin/goods-brands')->with('error', 'Invalid goods group selected.');
            }
            $brands = $this->goodsBrandModel->getBrandsByGroupWithItemCount($groupId);
        } else {
            $brands = $this->goodsBrandModel->getBrandsWithGroup();
        }
        
        // Get all groups for filter dropdown
        $groups = $this->goodsGroupModel->getActiveGroupsForDropdown();
        
        $data = [
            'title' => 'Goods Brands Management',
            'brands' => $brands,
            'groups' => $groups,
            'selected_group' => $selectedGroup,
            'group_id' => $groupId
        ];
        
        return view('goods_brands/goods_brands_index', $data);
    }
    
    /**
     * Show create form
     */
    public function new()
    {
        $authCheck = $this->checkAuth();
        if ($authCheck) return $authCheck;
        
        // Get group ID from query parameter if provided
        $groupId = $this->request->getGet('group_id');
        $selectedGroup = null;
        
        if ($groupId) {
            $selectedGroup = $this->goodsGroupModel->find($groupId);
        }
        
        // Get all active groups for dropdown
        $groups = $this->goodsGroupModel->getActiveGroupsForDropdown();
        
        $data = [
            'title' => 'Add New Goods Brand',
            'brand' => [],
            'groups' => $groups,
            'selected_group' => $selectedGroup,
            'group_id' => $groupId
        ];
        
        return view('goods_brands/goods_brands_create', $data);
    }
    
    /**
     * Handle create form submission
     */
    public function create()
    {
        $authCheck = $this->checkAuth();
        if ($authCheck) return $authCheck;
        
        $rules = [
            'goods_group_id' => 'required|integer',
            'brand_name' => 'required|max_length[150]',
            'type' => 'required|in_list[primary,substitute]'
        ];
        
        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }
        
        // Check if group exists and is active
        $group = $this->goodsGroupModel->find($this->request->getPost('goods_group_id'));
        if (!$group || $group['status'] !== 'active') {
            return redirect()->back()->withInput()->with('error', 'Selected goods group is not available.');
        }
        
        $data = [
            'goods_group_id' => $this->request->getPost('goods_group_id'),
            'brand_name' => $this->request->getPost('brand_name'),
            'type' => $this->request->getPost('type'),
            'status' => 'active', // Default status is active
            'status_by' => session()->get('user_id'),
            'status_at' => date('Y-m-d H:i:s'),
            'created_by' => session()->get('user_id')
        ];

        try {
            if ($this->goodsBrandModel->save($data)) {
                $redirectUrl = 'admin/goods-brands';
                if ($this->request->getPost('goods_group_id')) {
                    $redirectUrl .= '?group_id=' . $this->request->getPost('goods_group_id');
                }
                return redirect()->to($redirectUrl)->with('success', 'Goods brand created successfully.');
            } else {
                return redirect()->back()->withInput()->with('error', 'Failed to create goods brand.');
            }
        } catch (\Exception $e) {
            return redirect()->back()->withInput()->with('error', 'Brand name already exists in this goods group.');
        }
    }
    
    /**
     * Show single goods brand
     */
    public function show($id)
    {
        $authCheck = $this->checkAuth();
        if ($authCheck) return $authCheck;
        
        $brand = $this->goodsBrandModel->select('goods_brands.*, goods_groups.group_name')
                                      ->join('goods_groups', 'goods_groups.id = goods_brands.goods_group_id', 'left')
                                      ->where('goods_brands.id', $id)
                                      ->where('goods_brands.is_deleted', false)
                                      ->first();
        
        if (!$brand) {
            return redirect()->to('admin/goods-brands')->with('error', 'Goods brand not found.');
        }
        
        // Get items count for this brand
        $itemModel = new \App\Models\GoodsItemModel();
        $itemsCount = $itemModel->where('goods_brand_id', $id)
                               ->where('is_deleted', false)
                               ->countAllResults();
        
        $data = [
            'title' => 'View Goods Brand',
            'brand' => $brand,
            'items_count' => $itemsCount
        ];
        
        return view('goods_brands/goods_brands_show', $data);
    }
    
    /**
     * Show edit form
     */
    public function edit($id)
    {
        $authCheck = $this->checkAuth();
        if ($authCheck) return $authCheck;
        
        $brand = $this->goodsBrandModel->find($id);
        
        if (!$brand) {
            return redirect()->to('admin/goods-brands')->with('error', 'Goods brand not found.');
        }
        
        // Get all active groups for dropdown
        $groups = $this->goodsGroupModel->getActiveGroupsForDropdown();
        
        $data = [
            'title' => 'Edit Goods Brand',
            'brand' => $brand,
            'groups' => $groups
        ];
        
        return view('goods_brands/goods_brands_edit', $data);
    }
    
    /**
     * Handle edit form submission
     */
    public function update($id)
    {
        $authCheck = $this->checkAuth();
        if ($authCheck) return $authCheck;
        
        $brand = $this->goodsBrandModel->find($id);
        
        if (!$brand) {
            return redirect()->to('admin/goods-brands')->with('error', 'Goods brand not found.');
        }
        
        $rules = [
            'goods_group_id' => 'required|integer',
            'brand_name' => 'required|max_length[150]',
            'type' => 'required|in_list[primary,substitute]',
            'status' => 'required|in_list[active,inactive]'
        ];
        
        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }
        
        // Check if group exists and is active
        $group = $this->goodsGroupModel->find($this->request->getPost('goods_group_id'));
        if (!$group || $group['status'] !== 'active') {
            return redirect()->back()->withInput()->with('error', 'Selected goods group is not available.');
        }
        
        $data = [
            'goods_group_id' => $this->request->getPost('goods_group_id'),
            'brand_name' => $this->request->getPost('brand_name'),
            'type' => $this->request->getPost('type'),
            'status' => $this->request->getPost('status'),
            'status_by' => session()->get('user_id'),
            'status_at' => date('Y-m-d H:i:s'),
            'updated_by' => session()->get('user_id')
        ];
        
        try {
            if ($this->goodsBrandModel->update($id, $data)) {
                return redirect()->to('admin/goods-brands')->with('success', 'Goods brand updated successfully.');
            } else {
                return redirect()->back()->withInput()->with('error', 'Failed to update goods brand.');
            }
        } catch (\Exception $e) {
            return redirect()->back()->withInput()->with('error', 'Brand name already exists in this goods group.');
        }
    }
    
    /**
     * Delete goods brand
     */
    public function delete($id)
    {
        $authCheck = $this->checkAuth();
        if ($authCheck) return $authCheck;
        
        $brand = $this->goodsBrandModel->find($id);
        
        if (!$brand) {
            return redirect()->to('admin/goods-brands')->with('error', 'Goods brand not found.');
        }

        // Check if brand has items
        if ($this->goodsBrandModel->hasItems($id)) {
            return redirect()->to('admin/goods-brands')->with('error', 'Cannot delete goods brand that has items associated with it.');
        }

        // Soft delete
        $deleteData = [
            'is_deleted' => true,
            'deleted_by' => session()->get('user_id'),
            'deleted_at' => date('Y-m-d H:i:s')
        ];

        if ($this->goodsBrandModel->update($id, $deleteData)) {
            return redirect()->to('admin/goods-brands')->with('success', 'Goods brand deleted successfully.');
        } else {
            return redirect()->to('admin/goods-brands')->with('error', 'Failed to delete goods brand.');
        }
    }
}
