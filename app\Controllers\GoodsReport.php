<?php

namespace App\Controllers;

use App\Models\GoodsGroupModel;
use App\Models\GoodsBrandModel;
use App\Models\GoodsItemModel;

class GoodsReport extends BaseController
{
    protected $goodsGroupModel;
    protected $goodsBrandModel;
    protected $goodsItemModel;

    public function __construct()
    {
        $this->goodsGroupModel = new GoodsGroupModel();
        $this->goodsBrandModel = new GoodsBrandModel();
        $this->goodsItemModel = new GoodsItemModel();
    }

    /**
     * Check admin authentication and handle field session transfer for admin/supervisor users
     */
    private function checkAuth()
    {
        // Check if logged in to admin portal
        if (!session()->get('admin_logged_in')) {
            // If user has field session and is admin or supervisor, transfer to admin session
            if (session()->get('field_logged_in') &&
                (session()->get('field_is_admin') == 1 || session()->get('field_is_supervisor') == 1)) {

                // Transfer session data to admin portal
                $sessionData = [
                    'admin_user_id' => session()->get('field_user_id'),
                    'admin_email' => session()->get('field_email'),
                    'admin_name' => session()->get('field_name'),
                    'admin_role' => session()->get('field_role'),
                    'admin_is_admin' => session()->get('field_is_admin'),
                    'admin_is_supervisor' => session()->get('field_is_supervisor'),
                    'admin_org_id' => session()->get('field_org_id'),
                    'admin_logged_in' => true
                ];

                // Set admin session (keep field session for dual access)
                session()->set($sessionData);
                return null; // Authentication successful
            } else {
                return redirect()->to('admin')->with('error', 'Please login to access the admin portal.');
            }
        }
        return null; // Authentication successful
    }

    /**
     * Display goods report
     */
    public function index()
    {
        // Check authentication
        $authCheck = $this->checkAuth();
        if ($authCheck) return $authCheck;

        // Get all items with their group and brand information
        $items = $this->goodsItemModel->where('is_deleted', false)
                                     ->orderBy('id', 'ASC')
                                     ->findAll();

        // Add group and brand information to each item
        foreach ($items as &$item) {
            // Get group information
            $group = $this->goodsGroupModel->find($item['goods_group_id']);
            $item['group_name'] = $group ? $group['group_name'] : 'Unknown';
            $item['group_description'] = $group ? $group['description'] : '';

            // Get brand information
            $brand = $this->goodsBrandModel->find($item['goods_brand_id']);
            $item['brand_name'] = $brand ? $brand['brand_name'] : 'Unknown';
            $item['brand_type'] = $brand ? $brand['type'] : 'unknown';
        }

        $data = [
            'title' => 'Goods Report',
            'items' => $items
        ];

        return view('goods_report/goods_report_index', $data);
    }
}
